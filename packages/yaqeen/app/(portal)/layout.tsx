import { auth } from "@/auth";
import { AppSidebar } from "@/components/app-sidebar";
import { PermissionProvider } from "@/components/client-permission-gate";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { redirect } from "next/navigation";
import { Suspense, type ReactNode } from "react";
import { getUserLocale } from "@/services/locale";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import UnlockVehicleWrapper from "./rental/_components/unlock-vehicle/layoutWrapper";
import { JotaiProvider } from "./jotai-provider";

export default async function Layout({ children }: { children: ReactNode }) {
  const session = await auth();
  const role = session?.roles.includes("rental:finance:admin") ? "finance" : "cse";
  const locale = await getUserLocale();
  if (!session) {
    return redirect("/auth");
  }

  console.log('ATIF TEst')

  return (
    <>
      <SidebarProvider defaultOpen={false}>
        <PermissionProvider>
          <Suspense fallback={<LoadingSpinner />}>
            <AppSidebar
              user={session?.user ?? null}
              side={(locale === "ar" ? "right" : "left") as "left" | "right" | undefined}
              role={role}
            />
          </Suspense>
          <SidebarInset>
            <section className="box-border flex min-h-[calc(100vh-70px)] flex-col">
              <main className="mb-5">
                <JotaiProvider><>
                  <UnlockVehicleWrapper />
                  {children}

                </></JotaiProvider>
              </main>
            </section>
          </SidebarInset>
        </PermissionProvider>
      </SidebarProvider>
    </>
  );
}
